# War Thunder数据集管理工具使用说明

## 简介

War Thunder数据集管理工具是专门为处理多人采集的War Thunder游戏数据集而开发的工具。该工具可以帮助您完成数据集质量评估、文件重命名和数据过滤等任务。

## 功能特性

### 1. 数据集质量评估
- 检查图像和标签文件的配对情况
- 筛选包含有效目标的文件（isvisible=true且包含flightModels）
- 统计bbox大小分布
- 自动删除无效文件

### 2. 文件重命名
- 按照 `batch{批次}_{学号}_{序号}` 格式重命名
- 例如：`batch1_202301_1.png` 和 `batch1_202301_1.json`
- 保持图像和标签文件的对应关系

### 3. 数据过滤
- 根据bbox大小过滤数据集
- 删除超过90像素的大目标
- 保留所有小于90像素的目标
- 生成大小分布统计图表

### 4. BBox可视化
- 随机选择不同大小的目标进行可视化
- 按大小分类：极小(<10px)、小(10-33px)、中(33-90px)、大(>90px)
- 生成单个样本图像和总览图
- 显示飞机名称和尺寸信息

## 安装要求

```bash
pip install numpy matplotlib pillow tqdm
```

## 使用方法

### 方法1: 交互式模式（推荐新手）

```bash
cd ~/env/mmdetection/data
python run_wt_dataset_demo.py --interactive
```

然后按照提示输入：
1. 数据集路径（默认D:/WTdataset）
2. 选择要执行的任务
3. 输入批次ID和学号（如果需要重命名）
4. 设置bbox大小过滤范围（如果需要过滤）

### 方法2: 命令行模式

#### 完整流程示例：
```bash
cd ~/env/mmdetection/data
python wt_dataset_manager.py \
    --dataset_path "D:/WTdataset" \
    --batch_id "1" \
    --student_id "202301" \
    --max_size 90 \
    --task all
```

#### 仅质量评估：
```bash
cd ~/env/mmdetection/data
python wt_dataset_manager.py \
    --dataset_path "D:/WTdataset" \
    --task validate
```

#### 仅文件重命名：
```bash
cd ~/env/mmdetection/data
python wt_dataset_manager.py \
    --dataset_path "D:/WTdataset" \
    --batch_id "1" \
    --student_id "202301" \
    --task rename
```

#### 仅数据过滤：
```bash
cd ~/env/mmdetection/data
python wt_dataset_manager.py \
    --dataset_path "D:/WTdataset" \
    --max_size 90 \
    --task filter
```

#### 仅BBox可视化：
```bash
cd ~/env/mmdetection/data
python wt_dataset_manager.py \
    --dataset_path "D:/WTdataset" \
    --vis_samples 10 \
    --task visualize
```

### 方法3: 测试模式

```bash
python run_wt_dataset_demo.py \
    --dataset_path "D:/WTdataset" \
    --batch_id "1" \
    --student_id "202301" \
    --test_mode
```

## 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--dataset_path` | 数据集路径 | D:/WTdataset | D:/WTdataset |
| `--batch_id` | 批次ID | 无 | 1 |
| `--student_id` | 学号 | 无 | 202301 |
| `--task` | 执行任务 | all | validate/rename/filter/visualize/all |
| `--max_size` | 最大bbox大小(超过删除) | 90 | 90 |
| `--vis_samples` | 每类可视化样本数 | 10 | 10 |

## 输出文件

处理完成后，会在数据集目录生成以下文件：

1. **dataset_summary_report.txt** - 处理总结报告
2. **bbox_size_distribution.png** - bbox大小分布图表
3. **bbox_visualization/** - BBox可视化目录
   - overview_bbox_samples.png (总览图)
   - tiny_*.png (极小目标样本)
   - small_*.png (小目标样本)
   - medium_*.png (中等目标样本)
   - large_*.png (大目标样本)
4. **重命名后的图像和标签文件**

## 使用流程建议

### 第一步：质量评估
```bash
python wt_dataset_manager.py --dataset_path "你的路径" --task validate
```

### 第二步：文件重命名
```bash
python wt_dataset_manager.py \
    --dataset_path "你的路径" \
    --batch_id "你的批次" \
    --student_id "你的学号" \
    --task rename
```

### 第三步：数据过滤
```bash
python wt_dataset_manager.py \
    --dataset_path "你的路径" \
    --task filter
```

## 常见问题

### Q: 路径格式问题？
A: Windows路径会自动转换为WSL格式。支持以下格式：
- Windows: `D:\WTdataset\WTdataset`
- WSL: `/mnt/d/WTdataset/WTdataset`

### Q: 没有找到有效目标？
A: 检查JSON文件中的目标是否满足条件：
- `isvisible: true`
- `m_blk_path` 包含 "flightModels"
- `bbox` 不全为0

### Q: 重命名后文件顺序乱了？
A: 工具会按照原始文件的数字顺序进行重命名，确保顺序一致。

### Q: bbox大小如何计算？
A: 使用对角线长度：`√(width² + height²)`

### Q: 可以恢复被删除的文件吗？
A: 建议在处理前备份原始数据。删除操作不可逆。

## 示例输出

```
War Thunder数据集管理工具 - 完整演示
============================================================
数据集路径: /mnt/e/WTdataset/WTdataset
批次ID: 1
学号: 202301
BBox大小过滤范围: 33-90 像素
测试模式: 否
============================================================

任务1: 数据集质量评估
============================================================
发现图像文件: 1709 个
发现标签文件: 1709 个
有效配对: 1707 个
有效目标总数: 9679 个
无效文件: 2 个

任务2: 文件重命名
============================================================
成功重命名 1707 对文件

任务3: 数据集bbox大小过滤
============================================================
保留文件: 1580 对
删除文件: 127 对

数据集处理总结报告
============================================================
原始图像文件数: 1709
有效配对数: 1707
有效目标总数: 9679
最终保留图像数: 1580
```

## 联系支持

如果在使用过程中遇到问题，请检查：
1. 数据集路径是否正确
2. 文件权限是否足够
3. 依赖包是否已安装

更多技术支持请联系开发团队。 