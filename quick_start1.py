#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
War Thunder数据集管理工具 - 快速启动脚本

这是一个简化的启动脚本，专门为学生设计，减少了复杂的参数配置。
只需要输入基本信息即可完成数据集处理。
"""

import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wt_dataset_manager import WTDatasetManager


def student_friendly_interface():
    """学生友好的交互界面"""
    print("🎮 War Thunder数据集管理工具 🎮")
    print("=" * 50)
    print("欢迎使用！这个工具将帮助您整理和优化数据集")
    print("=" * 50)

    # 步骤1: 获取基本信息
    print("\n📁 步骤1: 输入基本信息")
    print("-" * 30)

    # 数据集路径
    print("请输入您的数据集路径:")
    print("  - Windows格式: D:\\WTdataset\\WTdataset")
    print("  - 或直接按回车使用默认路径: D:\\WTdataset")
    dataset_path = input("数据集路径: ").strip()

    if not dataset_path:
        dataset_path = "D:\\WTdataset"

    dataset_path = os.path.normpath(dataset_path)  # 标准化路径

    # 批次和学号
    batch_id = input("请输入批次号 (如: 1): ").strip()
    student_id = input("请输入学号 (如: 202301): ").strip()

    if not batch_id or not student_id:
        print("❌ 错误: 批次号和学号不能为空")
        return

    # 验证路径
    if not os.path.exists(dataset_path):
        print(f"❌ 错误: 数据集路径不存在: {dataset_path}")
        print("请检查路径是否正确")
        return

    print(f"\n✅ 设置确认:")
    print(f"   数据集路径: {dataset_path}")
    print(f"   批次号: {batch_id}")
    print(f"   学号: {student_id}")

    confirm = input("\n是否开始处理? (y/n): ").strip().lower()
    if confirm != 'y':
        print("取消处理")
        return

    # 步骤2: 执行处理流程
    print("\n🔍 步骤2: 数据集质量检测")
    print("-" * 30)

    manager = WTDatasetManager(dataset_path)

    # 质量评估
    valid_pairs = manager.validate_dataset()

    if not valid_pairs:
        print("❌ 没有找到有效的数据，请检查数据集")
        return

    print(f"✅ 找到 {len(valid_pairs)} 对有效的图像-标签文件")

    # 步骤3: 文件重命名
    print(f"\n📝 步骤3: 文件重命名 (batch{batch_id}_{student_id}_xxx)")
    print("-" * 30)

    rename_confirm = input("是否执行文件重命名? (y/n): ").strip().lower()
    if rename_confirm == 'y':
        manager.rename_files(batch_id, student_id)
        print("✅ 文件重命名完成")
    else:
        print("⏩ 跳过文件重命名")

    # 步骤4: 数据过滤
    print(f"\n🔧 步骤4: 数据质量过滤")
    print("-" * 30)
    print("过滤条件: 删除大于90像素的超大目标，保留所有小于90像素的目标")

    filter_confirm = input("是否执行数据过滤? (y/n): ").strip().lower()
    if filter_confirm == 'y':
        manager.filter_by_bbox_size(max_size=90)
        print("✅ 数据过滤完成")
    else:
        print("⏩ 跳过数据过滤")

    # 步骤5: BBox可视化
    print(f"\n🎨 步骤5: BBox可视化")
    print("-" * 30)
    print("随机选择不同大小的目标进行可视化展示")

    vis_confirm = input("是否生成BBox可视化? (y/n): ").strip().lower()
    if vis_confirm == 'y':
        manager.visualize_bbox_samples(num_samples=10)
        print("✅ 可视化完成")
    else:
        print("⏩ 跳过可视化")

    # 步骤6: 生成报告
    print(f"\n📊 步骤6: 生成处理报告")
    print("-" * 30)
    manager.generate_summary_report()

    print("\n🎉 数据集处理完成!")
    print("=" * 50)
    print("📋 处理结果:")
    print(f"   - 原始图像: {manager.stats['total_images']} 个")
    print(f"   - 有效配对: {manager.stats['valid_pairs']} 个")
    print(f"   - 有效目标: {manager.stats['valid_targets']} 个")
    if manager.stats['filtered_images'] > 0:
        print(f"   - 最终保留: {manager.stats['filtered_images']} 个")

    print(f"\n📁 输出文件保存在: {dataset_path}")
    print("   - dataset_summary_report.txt (处理报告)")
    print("   - bbox_size_distribution.png (大小分布图)")
    print("   - 重命名后的图像和标签文件")

    print("\n感谢使用War Thunder数据集管理工具! 🎮")


def quick_start_menu():
    """快速启动菜单"""
    while True:
        print("\n🎮 War Thunder数据集管理工具 - 快速启动")
        print("=" * 50)
        print("请选择操作模式:")
        print("1. 📚 完整处理流程 (推荐)")
        print("2. 🔍 仅检测数据集质量")
        print("3. 🎨 仅生成BBox可视化")
        print("4. 📖 查看使用说明")
        print("5. 🚪 退出")

        choice = input("\n请输入选项 (1-5): ").strip()

        if choice == '1':
            student_friendly_interface()
            break
        elif choice == '2':
            quick_check()
        elif choice == '3':
            quick_visualize()
        elif choice == '4':
            show_help()
        elif choice == '5':
            print("再见! 👋")
            break
        else:
            print("❌ 无效选项，请重新选择")


def quick_check():
    """快速检测模式"""
    print("\n🔍 快速检测模式")
    print("-" * 30)

    dataset_path = input("请输入数据集路径 (默认: D:\\WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "D:\\WTdataset"

    dataset_path = os.path.normpath(dataset_path)

    if not os.path.exists(dataset_path):
        print(f"❌ 路径不存在: {dataset_path}")
        return

    manager = WTDatasetManager(dataset_path)
    manager.validate_dataset()

    print(f"\n📊 检测结果:")
    print(f"   图像文件: {manager.stats['total_images']} 个")
    print(f"   标签文件: {manager.stats['total_labels']} 个")
    print(f"   有效配对: {manager.stats['valid_pairs']} 个")
    print(f"   有效目标: {manager.stats['valid_targets']} 个")


def quick_visualize():
    """快速可视化模式"""
    print("\n🎨 快速可视化模式")
    print("-" * 30)

    dataset_path = input("请输入数据集路径 (默认: D:\\WTdataset): ").strip()
    if not dataset_path:
        dataset_path = "D:\\WTdataset"

    dataset_path = os.path.normpath(dataset_path)

    if not os.path.exists(dataset_path):
        print(f"❌ 路径不存在: {dataset_path}")
        return

    num_samples = input("每个大小类别的样本数量 (默认: 10): ").strip()
    if not num_samples:
        num_samples = 10
    else:
        num_samples = int(num_samples)

    print(f"\n🎨 正在生成BBox可视化...")
    manager = WTDatasetManager(dataset_path)
    manager.visualize_bbox_samples(num_samples=num_samples)

    print(f"\n✅ 可视化完成!")
    print(f"📁 结果保存在: {os.path.join(dataset_path, 'bbox_visualization')}")


def show_help():
    """显示帮助信息"""
    print("\n📖 使用说明")
    print("=" * 50)
    print("这个工具可以帮助您:")
    print("1. 检查数据集质量，删除无效文件")
    print("2. 按照统一格式重命名文件: batch批次_学号_序号")
    print("3. 过滤掉超大目标(>90像素)，保留小目标")
    print("4. 生成BBox可视化样本")
    print("5. 生成详细的处理报告和统计图表")
    print()
    print("📋 数据要求:")
    print("- 图像文件: output_images*.png")
    print("- 标签文件: video_label*.json")
    print("- 目标必须: isvisible=true 且包含 flightModels")
    print()
    print("📊 BBox大小分类:")
    print("- 极小目标: < 10px 对角线")
    print("- 小目标: 10-33px 对角线")
    print("- 中等目标: 33-90px 对角线")
    print("- 大目标: > 90px 对角线 (会被过滤)")
    print()
    print("📁 建议的数据集结构:")
    print("WTdataset/")
    print("├── output_images0.png")
    print("├── video_label0.json")
    print("├── output_images1.png")
    print("├── video_label1.json")
    print("└── ...")
    print()
    input("按回车键返回主菜单...")


if __name__ == "__main__":
    try:
        quick_start_menu()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见!")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请联系技术支持")
