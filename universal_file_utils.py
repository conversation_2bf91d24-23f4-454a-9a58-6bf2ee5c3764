#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用文件查找工具
支持重命名前后的文件格式兼容
"""

import os
import glob
import re

def find_image_label_pairs(dataset_path):
    """
    查找图像和标签文件对
    兼容重命名前后的文件格式
    
    Args:
        dataset_path (str): 数据集路径
    
    Returns:
        list: [(image_file, label_file), ...] 文件对列表
    """
    pairs = []
    
    # 方法1: 查找原始格式文件 (output_images*.png, video_label*.json)
    original_images = glob.glob(os.path.join(dataset_path, "output_images*.png"))
    for image_file in original_images:
        base_name = os.path.basename(image_file)
        number = base_name.replace("output_images", "").replace(".png", "")
        label_file = os.path.join(dataset_path, f"video_label{number}.json")
        
        if os.path.exists(label_file):
            pairs.append((image_file, label_file))
    
    # 方法2: 查找重命名后格式文件 (batch*_*_*.png, batch*_*_*.json)
    renamed_images = glob.glob(os.path.join(dataset_path, "batch*_*_*.png"))
    for image_file in renamed_images:
        base_name = os.path.basename(image_file).replace(".png", "")
        label_file = os.path.join(dataset_path, f"{base_name}.json")
        
        if os.path.exists(label_file):
            pairs.append((image_file, label_file))
    
    # 方法3: 通用PNG和JSON配对 (兜底方案)
    if not pairs:
        all_images = glob.glob(os.path.join(dataset_path, "*.png"))
        for image_file in all_images:
            base_name = os.path.basename(image_file).replace(".png", "")
            label_file = os.path.join(dataset_path, f"{base_name}.json")
            
            if os.path.exists(label_file):
                pairs.append((image_file, label_file))
    
    # 去重并排序
    pairs = list(set(pairs))
    pairs.sort(key=lambda x: os.path.basename(x[0]))
    
    return pairs

def detect_file_format(dataset_path):
    """
    检测数据集使用的文件命名格式
    
    Args:
        dataset_path (str): 数据集路径
    
    Returns:
        str: 'original', 'renamed', 'mixed', 'unknown'
    """
    original_count = len(glob.glob(os.path.join(dataset_path, "output_images*.png")))
    renamed_count = len(glob.glob(os.path.join(dataset_path, "batch*_*_*.png")))
    
    if original_count > 0 and renamed_count == 0:
        return 'original'
    elif original_count == 0 and renamed_count > 0:
        return 'renamed'
    elif original_count > 0 and renamed_count > 0:
        return 'mixed'
    else:
        return 'unknown'

def find_all_images(dataset_path):
    """
    查找所有图像文件，兼容所有格式
    
    Args:
        dataset_path (str): 数据集路径
    
    Returns:
        list: 图像文件路径列表
    """
    images = []
    
    # 查找原始格式
    images.extend(glob.glob(os.path.join(dataset_path, "output_images*.png")))
    
    # 查找重命名格式
    images.extend(glob.glob(os.path.join(dataset_path, "batch*_*_*.png")))
    
    # 如果都没找到，查找所有PNG文件
    if not images:
        images = glob.glob(os.path.join(dataset_path, "*.png"))
    
    return sorted(list(set(images)))

def get_matching_label_file(image_file):
    """
    根据图像文件路径获取对应的标签文件路径
    
    Args:
        image_file (str): 图像文件路径
    
    Returns:
        str or None: 标签文件路径，如果不存在返回None
    """
    base_dir = os.path.dirname(image_file)
    base_name = os.path.basename(image_file)
    
    # 原始格式: output_images123.png -> video_label123.json
    if base_name.startswith("output_images"):
        number = base_name.replace("output_images", "").replace(".png", "")
        label_file = os.path.join(base_dir, f"video_label{number}.json")
    
    # 重命名格式: batch1_20230001_1.png -> batch1_20230001_1.json
    elif base_name.startswith("batch"):
        label_file = os.path.join(base_dir, base_name.replace(".png", ".json"))
    
    # 通用格式: 直接替换扩展名
    else:
        label_file = os.path.join(base_dir, base_name.replace(".png", ".json"))
    
    return label_file if os.path.exists(label_file) else None

def print_dataset_info(dataset_path):
    """
    打印数据集文件格式信息
    
    Args:
        dataset_path (str): 数据集路径
    """
    format_type = detect_file_format(dataset_path)
    pairs = find_image_label_pairs(dataset_path)
    
    print("=" * 60)
    print("数据集文件格式信息")
    print("=" * 60)
    print(f"数据集路径: {dataset_path}")
    print(f"文件格式: {format_type}")
    print(f"有效文件对数量: {len(pairs)}")
    
    if format_type == 'original':
        print("✅ 使用原始文件格式 (output_images*.png, video_label*.json)")
    elif format_type == 'renamed':
        print("✅ 使用重命名文件格式 (batch*_*_*.png, batch*_*_*.json)")
    elif format_type == 'mixed':
        print("⚠️  检测到混合文件格式，建议统一命名")
    else:
        print("❌ 未知文件格式或文件不存在")
    
    # 显示文件样例
    if pairs:
        print(f"\n文件样例:")
        for i, (img, lbl) in enumerate(pairs[:3]):
            print(f"  {i+1}. {os.path.basename(img)} <-> {os.path.basename(lbl)}")
        if len(pairs) > 3:
            print(f"  ... 还有 {len(pairs)-3} 对文件")

if __name__ == "__main__":
    # 测试函数
    dataset_path = "/mnt/e/WTdataset/WTdataset"
    print_dataset_info(dataset_path) 