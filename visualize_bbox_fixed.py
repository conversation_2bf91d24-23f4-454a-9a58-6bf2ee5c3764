#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的BBox可视化脚本
处理坐标范围问题
"""

import os
import json
import glob
import random
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def visualize_valid_bbox(dataset_path="/mnt/e/WTdataset/WTdataset", output_dir=None, num_samples=10):
    """可视化有效的bbox"""
    if output_dir is None:
        output_dir = os.path.join(dataset_path, "bbox_visualization_fixed")
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("🎯 开始修复版本的BBox可视化")
    print("=" * 60)
    
    # 收集所有有效目标
    valid_targets = []
    invalid_targets = []
    
    # 使用通用文件查找方法
    try:
        from universal_file_utils import find_image_label_pairs
        all_pairs = find_image_label_pairs(dataset_path)
        print(f"找到 {len(all_pairs)} 对文件")
    except ImportError:
        # 降级到原始方法
        image_files = glob.glob(os.path.join(dataset_path, "output_images*.png"))
        all_pairs = []
        for image_file in image_files:
            base_name = os.path.basename(image_file)
            number = base_name.replace("output_images", "").replace(".png", "")
            label_file = os.path.join(dataset_path, f"video_label{number}.json")
            if os.path.exists(label_file):
                all_pairs.append((image_file, label_file))
    
    for image_file, label_file in all_pairs:
        
        try:
            # 读取图像尺寸
            img = Image.open(image_file)
            img_width, img_height = img.size
            
            # 读取标签
            with open(label_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for unit in data.get('units', []):
                # 检查筛选条件
                if not (unit.get('isvisible', False) and "flightModels" in unit.get('m_blk_path', '')):
                    continue
                
                bbox = unit.get('bbox', None)
                if not bbox:
                    continue
                
                x1, y1, x2, y2 = bbox
                width = abs(x2 - x1)
                height = abs(y2 - y1)
                diagonal = np.sqrt(width**2 + height**2)
                
                # 检查bbox是否在图像范围内且有效
                if (0 <= x1 < img_width and 0 <= y1 < img_height and 
                    0 <= x2 < img_width and 0 <= y2 < img_height and
                    x1 < x2 and y1 < y2 and width > 0 and height > 0):
                    
                    valid_targets.append({
                        'image_file': image_file,
                        'bbox': bbox,
                        'diagonal': diagonal,
                        'width': width,
                        'height': height,
                        'img_width': img_width,
                        'img_height': img_height,
                        'aircraft_name': unit.get('m_short_name', 'Unknown'),
                        'size_category': get_size_category(diagonal)
                    })
                else:
                    invalid_targets.append({
                        'image_file': image_file,
                        'bbox': bbox,
                        'diagonal': diagonal,
                        'reason': get_invalid_reason(bbox, img_width, img_height)
                    })
        
        except Exception as e:
            print(f"处理文件 {image_file} 时出错: {e}")
    
    print(f"📊 统计结果:")
    print(f"  有效目标: {len(valid_targets)}")
    print(f"  无效目标: {len(invalid_targets)}")
    
    # 按尺寸分类
    size_stats = {}
    for target in valid_targets:
        category = target['size_category']
        if category not in size_stats:
            size_stats[category] = []
        size_stats[category].append(target)
    
    print(f"\n📏 有效目标尺寸分布:")
    for category, targets in size_stats.items():
        print(f"  {category}: {len(targets)}个")
    
    # 从每个类别随机选择样本进行可视化
    visualized_count = 0
    for category, targets in size_stats.items():
        if not targets:
            continue
            
        samples_per_category = min(3, len(targets))  # 每类最多3个样本
        selected_targets = random.sample(targets, samples_per_category)
        
        for i, target in enumerate(selected_targets):
            if visualized_count >= num_samples:
                break
                
            try:
                visualize_single_target(target, output_dir, f"{category}_{i+1}")
                visualized_count += 1
            except Exception as e:
                print(f"可视化目标时出错: {e}")
        
        if visualized_count >= num_samples:
            break
    
    # 生成统计报告
    generate_fixed_report(valid_targets, invalid_targets, output_dir)
    
    print(f"\n✅ 可视化完成！")
    print(f"📁 输出目录: {output_dir}")
    print(f"🖼️  生成图像: {visualized_count}张")

def get_size_category(diagonal):
    """根据对角线长度分类"""
    if diagonal < 10:
        return "极小目标(<10px)"
    elif diagonal < 33:
        return "小目标(10-33px)"
    elif diagonal <= 90:
        return "中等目标(33-90px)"
    else:
        return "大目标(>90px)"

def get_invalid_reason(bbox, img_width, img_height):
    """获取bbox无效的原因"""
    x1, y1, x2, y2 = bbox
    reasons = []
    
    if x1 < 0 or y1 < 0 or x2 < 0 or y2 < 0:
        reasons.append("坐标为负")
    if x1 >= img_width or x2 >= img_width:
        reasons.append("X坐标超出图像宽度")
    if y1 >= img_height or y2 >= img_height:
        reasons.append("Y坐标超出图像高度")
    if x1 >= x2 or y1 >= y2:
        reasons.append("bbox尺寸无效")
    
    return "; ".join(reasons) if reasons else "未知原因"

def visualize_single_target(target, output_dir, suffix):
    """可视化单个目标"""
    # 读取图像
    img = Image.open(target['image_file'])
    draw = ImageDraw.Draw(img)
    
    # 绘制bbox
    bbox = target['bbox']
    x1, y1, x2, y2 = bbox
    
    # 绘制红色矩形框
    draw.rectangle([x1, y1, x2, y2], outline='red', width=3)
    
    # 添加标签文本
    label_text = f"{target['aircraft_name']}\n{target['diagonal']:.1f}px"
    
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 计算文本位置
    text_x = max(0, x1)
    text_y = max(0, y1 - 40) if y1 > 40 else y2 + 5
    
    # 绘制文本背景
    bbox_text = draw.textbbox((text_x, text_y), label_text, font=font)
    draw.rectangle(bbox_text, fill='red', outline='red')
    
    # 绘制文本
    draw.text((text_x, text_y), label_text, fill='white', font=font)
    
    # 保存图像
    base_name = os.path.basename(target['image_file'])
    output_name = f"bbox_{suffix}_{base_name}"
    output_path = os.path.join(output_dir, output_name)
    img.save(output_path)
    
    print(f"  ✅ 保存: {output_name}")

def generate_fixed_report(valid_targets, invalid_targets, output_dir):
    """生成修复版本的统计报告"""
    report_path = os.path.join(output_dir, "bbox_analysis_fixed.txt")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("War Thunder 数据集 BBox 分析报告（修复版本）\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"总体统计:\n")
        f.write(f"  有效目标: {len(valid_targets)}\n")
        f.write(f"  无效目标: {len(invalid_targets)}\n")
        f.write(f"  有效率: {len(valid_targets)/(len(valid_targets)+len(invalid_targets))*100:.1f}%\n\n")
        
        # 有效目标尺寸分布
        size_stats = {}
        for target in valid_targets:
            category = target['size_category']
            if category not in size_stats:
                size_stats[category] = []
            size_stats[category].append(target['diagonal'])
        
        f.write("有效目标尺寸分布:\n")
        for category, diagonals in size_stats.items():
            f.write(f"  {category}: {len(diagonals)}个\n")
            f.write(f"    平均大小: {np.mean(diagonals):.1f}px\n")
            f.write(f"    尺寸范围: {min(diagonals):.1f} - {max(diagonals):.1f}px\n")
        
        f.write(f"\n无效目标原因分析:\n")
        reason_stats = {}
        for target in invalid_targets:
            reason = target['reason']
            reason_stats[reason] = reason_stats.get(reason, 0) + 1
        
        for reason, count in reason_stats.items():
            f.write(f"  {reason}: {count}个\n")
    
    print(f"📄 报告已保存: {report_path}")

if __name__ == "__main__":
    visualize_valid_bbox() 