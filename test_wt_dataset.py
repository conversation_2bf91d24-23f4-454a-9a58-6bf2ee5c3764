#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
War Thunder数据集测试脚本
用于快速测试和验证数据集质量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wt_dataset_manager import WTDatasetManager

def quick_test(dataset_path="/mnt/e/WTdataset/WTdataset"):
    """快速测试数据集质量"""
    print("War Thunder数据集快速质量检测")
    print("=" * 50)
    
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return
    
    # 创建管理器
    manager = WTDatasetManager(dataset_path)
    
    # 只执行验证任务
    print("正在检测数据集质量...")
    valid_pairs = manager.validate_dataset()
    
    # 显示简要统计
    print(f"\n快速检测结果:")
    print(f"有效图像-标签配对: {len(valid_pairs)} 个")
    print(f"有效目标总数: {manager.stats['valid_targets']} 个")
    
    # 显示bbox大小分布
    print(f"\nBBox大小分布预览:")
    for size_type, count in manager.stats['bbox_stats'].items():
        print(f"  {size_type}: {count} 个")
    
    return manager.stats

if __name__ == "__main__":
    quick_test() 